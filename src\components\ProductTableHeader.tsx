import React from "react";
import { Checkbox } from "@/components/ui/checkbox";
import { But<PERSON> } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { TrackedProduct } from "@/types/database";

interface ProductTableHeaderProps {
  products: TrackedProduct[];
  selectedProducts: string[];
  deleteMultipleProductsPending: boolean;
  onSelectionChange: (ids: string[]) => void;
  onDeleteSelectedProducts: () => void;
}

export const ProductTableHeader = ({
  products,
  selectedProducts,
  deleteMultipleProductsPending,
  onSelectionChange,
  onDeleteSelectedProducts,
}: ProductTableHeaderProps) => {
  const allSelected =
    products.length > 0 && selectedProducts.length === products.length;
  const someSelected = selectedProducts.length > 0 && !allSelected;

  const handleSelectAll = () => {
    if (allSelected) {
      onSelectionChange([]);
    } else {
      onSelectionChange(products.map((p) => p.id));
    }
  };

  return (
    <thead className="sticky top-0 z-30 border-b border-border">
      <tr className="text-xs bg-muted/95 backdrop-blur-sm">
        <th className="p-3 text-left font-medium text-muted-foreground w-12 bg-muted/95 backdrop-blur-sm">
          <Checkbox
            checked={allSelected}
            ref={(el) => {
              if (el && "indeterminate" in el) {
                (el as any).indeterminate = someSelected;
              }
            }}
            onCheckedChange={handleSelectAll}
          />
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Variant
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Provider
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Cost
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Shipping
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Discount
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Margin
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Price
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Fees
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground bg-muted/95 backdrop-blur-sm">
          Profit
        </th>
        <th className="p-3 text-left font-medium text-muted-foreground w-16 bg-muted/95 backdrop-blur-sm">
          {selectedProducts.length > 0 && (
            <Button
              size="sm"
              variant="destructive"
              onClick={onDeleteSelectedProducts}
              disabled={deleteMultipleProductsPending}
              className="h-7 w-7 p-0"
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          )}
        </th>
      </tr>
    </thead>
  );
};
