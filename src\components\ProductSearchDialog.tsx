import React, { useState } from "react";
import { Search, Plus, Loader2, Info } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAutoCostCalculation } from "@/hooks/useAutoCostCalculation";
import { SizeGroupedVariantSelector } from "@/components/SizeGroupedVariantSelector";

interface Blueprint {
  id: number;
  title: string;
  description: string;
  brand: string;
  model: string;
}

interface PrintProvider {
  id: number;
  title: string;
  location: string;
}

interface Variant {
  id: number;
  title: string;
  options: {
    color?: string;
    size?: string;
    [key: string]: any;
  };
  cost?: number;
  shipping_cost?: number;
  production_cost_note?: string;
}

interface ProductSearchDialogProps {
  variant?: "header" | "section";
  onProductsAdded?: () => void;
}

export const ProductSearchDialog = ({ variant = "section", onProductsAdded }: ProductSearchDialogProps) => {
  const [open, setOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [searchResults, setSearchResults] = useState<Blueprint[]>([]);
  const [selectedBlueprint, setSelectedBlueprint] = useState<Blueprint | null>(null);
  const [printProviders, setPrintProviders] = useState<PrintProvider[]>([]);
  const [selectedPrintProvider, setSelectedPrintProvider] = useState<PrintProvider | null>(null);
  const [variants, setVariants] = useState<Variant[]>([]);
  const [selectedVariantIds, setSelectedVariantIds] = useState<number[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isLoadingProviders, setIsLoadingProviders] = useState(false);
  const [isLoadingVariants, setIsLoadingVariants] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [hasSearched, setHasSearched] = useState(false);
  const [groupingAttribute, setGroupingAttribute] = useState<string>("size");
  const [availableAttributes, setAvailableAttributes] = useState<string[]>([]);
  
  const { toast } = useToast();
  const { getCostsAutomatically, isGettingCosts } = useAutoCostCalculation();

  const searchBlueprints = async () => {
    if (!searchTerm.trim()) return;
    
    setIsSearching(true);
    setHasSearched(true);
    setSearchResults([]);
    
    try {
      console.log('Searching for:', searchTerm);
      
      const response = await supabase.functions.invoke('printify-catalog', {
        body: { action: 'search-blueprints', search: searchTerm }
      });
      
      console.log('Search response:', response);
      
      if (response.error) {
        console.error('Search error:', response.error);
        throw new Error(response.error.message || 'Search failed');
      }
      
      const results = response.data?.data || response.data || [];
      console.log('Search results:', results);
      
      setSearchResults(results);
      
      if (results.length === 0) {
        toast({
          title: "No Results Found",
          description: `No products found for "${searchTerm}". Try different keywords like "mug", "t-shirt", "hoodie", "canvas", or "print".`,
          variant: "default"
        });
      } else {
        toast({
          title: "Search Complete",
          description: `Found ${results.length} product${results.length > 1 ? 's' : ''} for "${searchTerm}".`,
          variant: "default"
        });
      }
      
    } catch (error) {
      console.error('Search error:', error);
      toast({
        title: "Search Failed",
        description: error.message || "Could not search products. Please check your Printify API configuration.",
        variant: "destructive"
      });
    } finally {
      setIsSearching(false);
    }
  };

  const selectBlueprint = async (blueprint: Blueprint) => {
    setSelectedBlueprint(blueprint);
    setSelectedPrintProvider(null);
    setVariants([]);
    setSelectedVariantIds([]);
    
    setIsLoadingProviders(true);
    try {
      const response = await supabase.functions.invoke('printify-catalog', {
        body: { action: 'get-blueprint-providers', blueprintId: blueprint.id }
      });
      
      if (response.error) throw new Error(response.error.message || 'Failed to load providers');
      setPrintProviders(response.data || []);
    } catch (error) {
      console.error('Error loading providers:', error);
      toast({
        title: "Error",
        description: error.message || "Could not load print providers.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingProviders(false);
    }
  };

  const selectPrintProvider = async (provider: PrintProvider) => {
    setSelectedPrintProvider(provider);
    setVariants([]);
    setSelectedVariantIds([]);
    setAvailableAttributes([]);
    setGroupingAttribute("size");
    
    if (!selectedBlueprint) return;
    
    setIsLoadingVariants(true);
    try {
      const response = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'get-blueprint-variants', 
          blueprintId: selectedBlueprint.id,
          printProviderId: provider.id
        }
      });
      
      if (response.error) throw new Error(response.error.message || 'Failed to load variants');
      
      const variantsData = response.data?.variants || [];
      setVariants(variantsData);
      
      // Extract available attributes from variants
      const attributesSet = new Set<string>();
      variantsData.forEach((variant: Variant) => {
        if (variant.options) {
          Object.keys(variant.options).forEach(key => {
            if (variant.options[key]) {
              attributesSet.add(key);
            }
          });
        }
      });
      
      const attributes = Array.from(attributesSet).sort();
      setAvailableAttributes(attributes);
      
      // Set default grouping to size if available, otherwise first available attribute
      if (attributes.includes('size')) {
        setGroupingAttribute('size');
      } else if (attributes.length > 0) {
        setGroupingAttribute(attributes[0]);
      }
      
    } catch (error) {
      console.error('Error loading variants:', error);
      toast({
        title: "Error",
        description: error.message || "Could not load variants.",
        variant: "destructive"
      });
    } finally {
      setIsLoadingVariants(false);
    }
  };

  const handleVariantSelectionChange = (variantIds: number[]) => {
    setSelectedVariantIds(variantIds);
  };

  const saveSelectedProducts = async () => {
    if (!selectedBlueprint || !selectedPrintProvider || selectedVariantIds.length === 0) {
      toast({
        title: "Incomplete Selection",
        description: "Please select a blueprint, print provider, and at least one variant.",
        variant: "destructive"
      });
      return;
    }

    setIsSaving(true);
    try {
      // First, ensure blueprint exists in our database
      const { data: existingBlueprint } = await supabase
        .from('blueprints')
        .select('id')
        .eq('printify_id', selectedBlueprint.id)
        .single();

      let blueprintId = existingBlueprint?.id;
      
      if (!blueprintId) {
        const { data: newBlueprint, error: blueprintError } = await supabase
          .from('blueprints')
          .insert({
            printify_id: selectedBlueprint.id,
            title: selectedBlueprint.title,
            description: selectedBlueprint.description,
            brand: selectedBlueprint.brand,
            model: selectedBlueprint.model
          })
          .select('id')
          .single();
          
        if (blueprintError) throw blueprintError;
        blueprintId = newBlueprint.id;
      }

      // Ensure print provider exists
      const { data: existingProvider } = await supabase
        .from('print_providers')
        .select('id')
        .eq('printify_id', selectedPrintProvider.id)
        .single();

      let providerId = existingProvider?.id;
      
      if (!providerId) {
        const { data: newProvider, error: providerError } = await supabase
          .from('print_providers')
          .insert({
            printify_id: selectedPrintProvider.id,
            title: selectedPrintProvider.title,
            location: selectedPrintProvider.location
          })
          .select('id')
          .single();
          
        if (providerError) throw providerError;
        providerId = newProvider.id;
      }

      // Get production costs for selected variants
      const costs = await new Promise<{ variantId: number; productionCost: number; shippingCost: number }[]>((resolve) => {
        getCostsAutomatically(
          selectedBlueprint.id,
          selectedPrintProvider.id,
          selectedVariantIds,
          (calculatedCosts) => {
            resolve(calculatedCosts);
          }
        );
      });

      // Add selected variants with calculated production costs
      const defaultProfitMargin = 40;
      const profitMargin = defaultProfitMargin / 100;
      
      const variantsToAdd = variants
        .filter(variant => selectedVariantIds.includes(variant.id))
        .map(variant => {
          const costData = costs.find(c => c.variantId === variant.id);
          const productionCost = costData ? (costData.productionCost / 100) : 0; // Convert cents to dollars
          const shippingCost = costData ? costData.shippingCost : 0;
          const totalCost = productionCost + shippingCost;
          
          let retailPrice = totalCost;
          if (profitMargin > 0 && profitMargin < 1) {
            retailPrice = totalCost / (1 - profitMargin);
          }
          const finalRetailPrice = Number(retailPrice.toFixed(2));

          return {
            blueprint_id: blueprintId,
            print_provider_id: providerId,
            printify_variant_id: variant.id,
            variant_title: variant.title,
            size: variant.options?.size || null,
            color: variant.options?.color || null,
            production_cost: productionCost,
            shipping_cost: shippingCost,
            profit_margin_percentage: defaultProfitMargin,
            retail_price: finalRetailPrice,
          };
        });

      console.log('Variants to add with costs:', variantsToAdd);

      const { error: variantsError } = await supabase
        .from('tracked_products')
        .upsert(variantsToAdd, { 
          onConflict: 'blueprint_id,print_provider_id,printify_variant_id'
        });

      if (variantsError) throw variantsError;

      toast({
        title: "Success",
        description: `Added ${selectedVariantIds.length} variant${selectedVariantIds.length > 1 ? 's' : ''} to tracking with production costs and 40% profit margin.`
      });

      // Reset form and close dialog
      setSearchTerm("");
      setSearchResults([]);
      setSelectedBlueprint(null);
      setSelectedPrintProvider(null);
      setPrintProviders([]);
      setVariants([]);
      setSelectedVariantIds([]);
      setAvailableAttributes([]);
      setGroupingAttribute("size");
      setOpen(false);
      
      // Notify parent component
      onProductsAdded?.();
    } catch (error) {
      console.error('Error saving products:', error);
      toast({
        title: "Error",
        description: "Could not save products. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsSaving(false);
    }
  };

  const resetDialog = () => {
    setSearchTerm("");
    setSearchResults([]);
    setSelectedBlueprint(null);
    setSelectedPrintProvider(null);
    setPrintProviders([]);
    setVariants([]);
    setSelectedVariantIds([]);
    setHasSearched(false);
    setAvailableAttributes([]);
    setGroupingAttribute("size");
  };

  const isHeaderVariant = variant === "header";

  // Popular search suggestions
  const searchSuggestions = [
    "t-shirt", "hoodie", "mug", "poster", "canvas", "tote bag", 
    "phone case", "sticker", "notebook", "pillow"
  ];

  return (
    <Dialog open={open} onOpenChange={(isOpen) => {
      setOpen(isOpen);
      if (!isOpen) resetDialog();
    }}>
      <DialogTrigger asChild>
        <Button className="bg-primary text-primary-foreground hover:bg-primary/90">
          <Plus className="h-4 w-4 mr-2" />
          Add Products
        </Button>
      </DialogTrigger>
      <DialogContent className="bg-gray-800 text-white border-gray-700 max-w-4xl max-h-[95vh] h-[95vh] overflow-hidden flex flex-col">
        <DialogHeader>
          <DialogTitle className="text-xl">Search Printify Catalog</DialogTitle>
          <DialogDescription className="text-gray-400">
            Search for products, select print providers, and choose variants to track.
          </DialogDescription>
        </DialogHeader>

        <div className="flex-1 overflow-y-auto space-y-6 pr-2">
          {/* Search Section */}
          <div>
            <Label htmlFor="search" className="text-gray-300 font-medium">Search Products</Label>
            <div className="flex gap-2 mt-2">
              <Input
                id="search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyDown={(e) => e.key === 'Enter' && searchBlueprints()}
                placeholder="e.g. t-shirt, mug, hoodie, poster..."
                className="bg-gray-700 border-gray-600 text-white placeholder:text-gray-400"
              />
              <Button 
                onClick={searchBlueprints} 
                disabled={isSearching || !searchTerm.trim()}
                className="bg-blue-600 hover:bg-blue-700 px-4"
                data-search-btn
              >
                {isSearching ? <Loader2 className="h-4 w-4 animate-spin" /> : <Search className="h-4 w-4" />}
              </Button>
            </div>
            
            {/* Search Suggestions */}
            <div className="mt-3">
              <p className="text-sm text-gray-400 mb-2">Popular searches:</p>
              <div className="flex flex-wrap gap-2">
                {searchSuggestions.map((suggestion) => (
                  <Button
                    key={suggestion}
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      setSearchTerm(suggestion);
                      // Auto-search when clicking a suggestion
                      setTimeout(() => {
                        searchBlueprints();
                      }, 100);
                    }}
                    className="text-xs bg-gray-700 border-gray-600 hover:bg-gray-600 text-gray-300"
                  >
                    {suggestion}
                  </Button>
                ))}
              </div>
            </div>
          </div>

          {/* Search Results */}
          {hasSearched && (
            <div>
              <Label className="text-gray-300 font-medium">
                Search Results {searchResults.length > 0 && `(${searchResults.length})`}
              </Label>
              {searchResults.length > 0 ? (
                <div className="grid gap-3 mt-3 max-h-48 overflow-y-auto">
                  {searchResults.map((blueprint) => (
                    <Button
                      key={blueprint.id}
                      variant="outline"
                      onClick={() => selectBlueprint(blueprint)}
                      className={`justify-start text-left h-auto p-4 ${
                        selectedBlueprint?.id === blueprint.id 
                          ? 'bg-blue-600 border-blue-500 text-white' 
                          : 'bg-gray-700 border-gray-600 hover:bg-gray-600 text-gray-100'
                      }`}
                    >
                      <div>
                        <div className="font-medium text-base">{blueprint.title}</div>
                        <div className="text-sm opacity-80 mt-1">{blueprint.brand} - {blueprint.model}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              ) : (
                <div className="text-gray-400 mt-3 p-4 bg-gray-700 rounded text-center">
                  <p>No products found for "{searchTerm}"</p>
                  <p className="text-sm mt-1">Try searching for: "mug", "t-shirt", "hoodie", "canvas", or "notebook"</p>
                </div>
              )}
            </div>
          )}

          {/* Print Providers */}
          {selectedBlueprint && (
            <div>
              <Label className="text-gray-300 font-medium">Print Providers</Label>
              {isLoadingProviders ? (
                <div className="flex items-center gap-2 mt-3 p-4 bg-gray-700 rounded">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-gray-400">Loading print providers...</span>
                </div>
              ) : (
                <Select onValueChange={(value) => {
                  const provider = printProviders.find(p => p.id.toString() === value);
                  if (provider) selectPrintProvider(provider);
                }}>
                  <SelectTrigger className="bg-gray-700 border-gray-600 text-white mt-3">
                    <SelectValue placeholder="Select a print provider" />
                  </SelectTrigger>
                  <SelectContent className="bg-gray-700 border-gray-600">
                    {printProviders.map((provider) => (
                      <SelectItem key={provider.id} value={provider.id.toString()}>
                        {provider.title} ({provider.location})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          )}

          {/* Grouping Options */}
          {selectedPrintProvider && availableAttributes.length > 1 && (
            <div>
              <Label className="text-gray-300 font-medium mb-3 block">Group Variants By</Label>
              <ToggleGroup 
                type="single" 
                value={groupingAttribute} 
                onValueChange={(value) => value && setGroupingAttribute(value)}
                className="justify-start"
              >
                {availableAttributes.map((attr) => (
                  <ToggleGroupItem 
                    key={attr} 
                    value={attr}
                    className="bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600 data-[state=on]:bg-blue-600 data-[state=on]:text-white"
                  >
                    {attr.charAt(0).toUpperCase() + attr.slice(1)}
                  </ToggleGroupItem>
                ))}
              </ToggleGroup>
              <p className="text-sm text-gray-400 mt-2">
                Choose how to group variants for easier selection. For example, group by color if you want to select specific colors only.
              </p>
            </div>
          )}

          {/* Variants section with SizeGroupedVariantSelector */}
          {selectedPrintProvider && (
            <div>
              <div className="flex items-center gap-2 mb-3">
                <Label className="text-gray-300 font-medium">Available Variants</Label>
                {isSaving && isGettingCosts && (
                  <div className="flex items-center gap-1 px-2 py-1 bg-blue-600/20 border border-blue-500/30 rounded text-xs text-blue-200">
                    <Loader2 className="h-3 w-3 animate-spin" />
                    <span>Getting costs from Printify...</span>
                  </div>
                )}
              </div>
              
              {isLoadingVariants ? (
                <div className="flex items-center gap-2 mt-3 p-4 bg-gray-700 rounded">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-gray-400">Loading variants...</span>
                </div>
              ) : variants.length > 0 ? (
                <SizeGroupedVariantSelector
                  variants={variants}
                  selectedVariantIds={selectedVariantIds}
                  onSelectionChange={handleVariantSelectionChange}
                  groupingAttribute={groupingAttribute}
                />
              ) : (
                <div className="text-gray-400 mt-3 p-4 bg-gray-700 rounded text-center">
                  No variants available for this combination.
                </div>
              )}
            </div>
          )}
        </div>

        {/* Save Button - Fixed at bottom */}
        {selectedVariantIds.length > 0 && (
          <div className="flex justify-end gap-3 pt-6 border-t border-gray-700 mt-4">
            <Button 
              variant="outline" 
              onClick={() => setOpen(false)}
              className="border-gray-600 text-gray-300 hover:bg-gray-700"
            >
              Cancel
            </Button>
            <Button 
              onClick={saveSelectedProducts}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              {isSaving ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Adding with costs...
                </>
              ) : (
                `Add ${selectedVariantIds.length} Variant${selectedVariantIds.length > 1 ? 's' : ''}`
              )}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
