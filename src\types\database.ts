
export interface Market {
  id: string;
  name: string;
  transaction_fee: number;
  transaction_fee_type: 'percentage' | 'flat';
  processing_fee: number;
  processing_fee_type: 'percentage' | 'flat';
  other_fee: number;
  other_fee_type: 'percentage' | 'flat';
  created_at: string;
  updated_at: string;
}

export interface Blueprint {
  id: string;
  printify_id: number;
  title: string;
  description?: string;
  brand?: string;
  model?: string;
  created_at: string;
}

export interface PrintProvider {
  id: string;
  printify_id: number;
  title: string;
  location?: string;
  created_at: string;
}

export interface TrackedProduct {
  id: string;
  blueprint_id: string;
  print_provider_id: string;
  printify_variant_id: number;
  variant_title: string;
  size?: string;
  color?: string;
  production_cost: number;
  shipping_cost: number;
  discount_percentage: number;
  profit_margin_percentage: number;
  retail_price?: number;
  is_active: boolean;
  notes?: string;
  offers_free_shipping: boolean;
  created_at: string;
  updated_at: string;
  blueprints?: Blueprint;
  print_providers?: PrintProvider;
}
