
import { Market, TrackedProduct } from '@/types/database';

export const calculateFees = (retailPrice: number, market: Market) => {
  const transaction = market.transaction_fee_type === 'percentage' 
    ? (retailPrice * market.transaction_fee) / 100
    : market.transaction_fee;
    
  const processing = market.processing_fee_type === 'percentage'
    ? (retailPrice * market.processing_fee) / 100
    : market.processing_fee;
    
  const other = market.other_fee_type === 'percentage'
    ? (retailPrice * market.other_fee) / 100
    : market.other_fee;
    
  const total = transaction + processing + other;
  
  return {
    transaction: Number(transaction.toFixed(2)),
    processing: Number(processing.toFixed(2)),
    other: Number(other.toFixed(2)),
    total: Number(total.toFixed(2))
  };
};

export const calculateDiscountedPrice = (originalPrice: number, discountPercentage: number) => {
  return originalPrice * (1 - discountPercentage / 100);
};

export const calculateProfit = (product: { production_cost: number; shipping_cost: number; retail_price?: number; discount_percentage: number; offers_free_shipping: boolean }, market: Market) => {
  if (product.retail_price === undefined || product.retail_price === null) return 0;
  
  const discountedPrice = calculateDiscountedPrice(product.retail_price, product.discount_percentage);
  const fees = calculateFees(discountedPrice, market);
  const shippingCostToConsider = product.offers_free_shipping ? product.shipping_cost : 0;
  const totalCosts = product.production_cost + shippingCostToConsider + fees.total;
  const profit = discountedPrice - totalCosts;
  
  return Number(profit.toFixed(2));
};

export const formatFeeDisplay = (fee: number, feeType: 'percentage' | 'flat') => {
  return feeType === 'percentage' ? `${fee}%` : `$${fee}`;
};

export const calculatePriceFromProfit = (
  desiredProfit: number,
  product: Pick<TrackedProduct, 'production_cost' | 'shipping_cost' | 'offers_free_shipping' | 'discount_percentage'>,
  market: Market
): number | null => {
  const totalFlatFees = (market.processing_fee_type === 'flat' ? market.processing_fee : 0) + (market.transaction_fee_type === 'flat' ? market.transaction_fee : 0) + (market.other_fee_type === 'flat' ? market.other_fee : 0);
  const totalPercentageFees = ((market.processing_fee_type === 'percentage' ? market.processing_fee : 0) + (market.transaction_fee_type === 'percentage' ? market.transaction_fee : 0) + (market.other_fee_type === 'percentage' ? market.other_fee : 0)) / 100;
  
  const baseCosts = product.production_cost + (product.offers_free_shipping ? product.shipping_cost : 0);
  const discountMultiplier = 1 - (product.discount_percentage / 100);
  
  if ((discountMultiplier - totalPercentageFees) <= 0) {
      return null;
  }

  const newPrice = (desiredProfit + baseCosts + totalFlatFees) / (discountMultiplier - totalPercentageFees);
  
  return newPrice >= 0 ? newPrice : null;
};

export const calculatePriceFromMargin = (
  desiredMarginPercent: number,
  product: Pick<TrackedProduct, 'production_cost' | 'shipping_cost' | 'offers_free_shipping' | 'discount_percentage'>,
  market: Market
): number | null => {
  const totalFlatFees = (market.processing_fee_type === 'flat' ? market.processing_fee : 0) + (market.transaction_fee_type === 'flat' ? market.transaction_fee : 0) + (market.other_fee_type === 'flat' ? market.other_fee : 0);
  const totalPercentageFees = ((market.processing_fee_type === 'percentage' ? market.processing_fee : 0) + (market.transaction_fee_type === 'percentage' ? market.transaction_fee : 0) + (market.other_fee_type === 'percentage' ? market.other_fee : 0)) / 100;
  
  const marginFraction = desiredMarginPercent / 100;
  const discountMultiplier = 1 - (product.discount_percentage / 100);

  if (discountMultiplier - marginFraction * discountMultiplier - totalPercentageFees <= 0) {
      return null;
  }

  const baseProductCosts = product.production_cost + (product.offers_free_shipping ? product.shipping_cost : 0);
  const baseCosts = baseProductCosts + totalFlatFees;
  const newPrice = baseCosts / (discountMultiplier - marginFraction * discountMultiplier - totalPercentageFees);

  return newPrice >= 0 ? newPrice : null;
};
