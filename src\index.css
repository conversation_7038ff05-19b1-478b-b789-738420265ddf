
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Definition of the design system. All colors, gradients, fonts, etc should be defined here. */

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Font size classes */
  .font-size-small {
    font-size: 14px;
  }

  .font-size-small h1 {
    font-size: 1.75rem;
  }
  
  .font-size-small h2 {
    font-size: 1.5rem;
  }
  
  .font-size-small h3 {
    font-size: 1.25rem;
  }
  
  .font-size-small p {
    font-size: 0.75rem;
  }
  
  .font-size-small table {
    font-size: 0.6875rem;
  }

  .font-size-default {
    font-size: 16px;
  }

  .font-size-default h1 {
    font-size: 2rem;
  }
  
  .font-size-default h2 {
    font-size: 1.75rem;
  }
  
  .font-size-default h3 {
    font-size: 1.5rem;
  }
  
  .font-size-default p {
    font-size: 0.875rem;
  }
  
  .font-size-default table {
    font-size: 0.75rem;
  }

  .font-size-large {
    font-size: 18px;
  }

  .font-size-large h1 {
    font-size: 2.25rem;
  }
  
  .font-size-large h2 {
    font-size: 2rem;
  }
  
  .font-size-large h3 {
    font-size: 1.75rem;
  }
  
  .font-size-large p {
    font-size: 1rem;
  }
  
  .font-size-large table {
    font-size: 0.875rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }

  /* Consistent button styling using theme-aware colors */
  .btn-primary {
    @apply bg-primary hover:bg-primary/90 text-primary-foreground transition-colors;
  }

  .btn-secondary {
    @apply bg-secondary hover:bg-secondary/80 text-secondary-foreground transition-colors;
  }

  .btn-destructive {
    @apply bg-destructive hover:bg-destructive/90 text-destructive-foreground transition-colors;
  }

  /* Consistent table styling with theme-aware colors */
  .table-header {
    @apply bg-muted text-muted-foreground font-bold text-xs uppercase tracking-wider;
  }

  .table-row-even {
    @apply bg-muted/30;
  }

  .table-row-odd {
    @apply bg-card;
  }

  .table-row-hover {
    @apply hover:bg-muted/50 transition-colors;
  }

  /* Consistent card styling using theme-aware colors */
  .card-dark {
    @apply bg-card/50 backdrop-blur-sm border border-border rounded-lg;
  }

  /* Global text size adjustments - now handled by font-size classes */
  h1 {
    @apply text-2xl;
  }
  
  h2 {
    @apply text-xl;
  }
  
  h3 {
    @apply text-lg;
  }
  
  p {
    @apply text-sm;
  }
  
  /* Table-specific smaller fonts */
  table {
    @apply text-xs;
  }
  
  table th {
    @apply text-xs;
  }
  
  table td {
    @apply text-xs;
  }
}
