
import React from "react";
import { ChevronDown, ChevronRight, Trash2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { TrackedProduct } from "@/types/database";

interface BlueprintHeaderRowProps {
  blueprint: string;
  blueprintProducts: TrackedProduct[];
  isCollapsed: boolean;
  allInBlueprintSelected: boolean;
  updateMultipleProductsPending: boolean;
  deleteMultipleProductsPending: boolean;
  onToggleCollapse: (blueprint: string) => void;
  onBlueprintSelectionToggle: (blueprintProducts: TrackedProduct[]) => void;
  onDeleteBlueprintProducts: (blueprint: string, blueprintProducts: TrackedProduct[]) => void;
  onBlueprintFreeShippingToggle: (blueprintProducts: TrackedProduct[], isFreeShipping: boolean) => void;
  isActiveBlueprint?: boolean;
  isBlocked?: boolean;
}

export const BlueprintHeaderRow = ({
  blueprint,
  blueprintProducts,
  isCollapsed,
  allInBlueprintSelected,
  deleteMultipleProductsPending,
  onToggleCollapse,
  onBlueprintSelectionToggle,
  onDeleteBlueprintProducts,
  onBlueprintFreeShippingToggle,
  isActiveBlueprint = false,
  isBlocked = false,
}: BlueprintHeaderRowProps) => {
  const getRowClasses = () => {
    let baseClasses = "transition-colors border-b border-gray-600";
    
    if (isActiveBlueprint) {
      return `${baseClasses} bg-blue-700/90 hover:bg-blue-700`;
    } else if (isBlocked) {
      return `${baseClasses} bg-gray-800/50 hover:bg-gray-800/70 opacity-60`;
    } else {
      return `${baseClasses} bg-gray-700/80 hover:bg-gray-700`;
    }
  };

  const allProductsOfferFreeShipping = blueprintProducts.every(p => p.offers_free_shipping);

  return (
    <tr className={getRowClasses()}>
      <td className="p-4">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={allInBlueprintSelected}
            onCheckedChange={() => onBlueprintSelectionToggle(blueprintProducts)}
            disabled={isBlocked}
          />
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onToggleCollapse(blueprint)}
            className="text-gray-200 hover:text-white p-0 h-auto hover:bg-transparent"
          >
            {isCollapsed ? (
              <ChevronRight className="h-4 w-4" />
            ) : (
              <ChevronDown className="h-4 w-4" />
            )}
          </Button>
        </div>
      </td>
      <td className="p-4 text-white font-bold text-base" colSpan={3}>
        <div className="flex items-center gap-2">
          <span className={isActiveBlueprint ? "text-blue-100" : "text-blue-200"}>
            {blueprint}
          </span>
          <span className="text-sm text-gray-300 font-normal">
            ({blueprintProducts.length} variants)
          </span>
          {isActiveBlueprint && (
            <span className="text-xs bg-blue-600 text-blue-100 px-2 py-1 rounded-full font-medium">
              Active
            </span>
          )}
          {isBlocked && (
            <span className="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded-full font-medium">
              Blocked
            </span>
          )}
        </div>
      </td>
      <td className="p-4 align-middle">
        <div className="flex items-center gap-2">
          <Checkbox
            checked={allProductsOfferFreeShipping}
            onCheckedChange={(checked) => onBlueprintFreeShippingToggle(blueprintProducts, !!checked)}
            disabled={isBlocked}
          />
          <span className="text-sm text-gray-300">Free Shipping</span>
        </div>
      </td>
      <td colSpan={5}></td>
      <td className="p-4 align-middle">
        <Button
          size="sm"
          variant="destructive"
          onClick={() => onDeleteBlueprintProducts(blueprint, blueprintProducts)}
          disabled={deleteMultipleProductsPending || isBlocked}
          className="bg-red-600 hover:bg-red-700 transition-colors"
        >
          <Trash2 className="h-4 w-4" />
        </Button>
      </td>
    </tr>
  );
};
