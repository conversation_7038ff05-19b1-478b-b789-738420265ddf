
import {
  Sidebar,
  <PERSON>bar<PERSON>ontent,
  SidebarHeader,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarFooter,
} from "@/components/ui/sidebar";
import { DollarSign, Home, Settings } from "lucide-react";
import { Link, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { UserProfileSection } from "./UserProfileSection";

export function AppSidebar() {
  const location = useLocation();

  return (
    <Sidebar className="bg-sidebar border-r border-sidebar-border">
      <SidebarHeader className="p-6 border-b border-sidebar-border bg-sidebar">
        <h2 className="text-2xl font-bold text-sidebar-foreground bg-gradient-to-r from-blue-400 to-purple-500 bg-clip-text text-transparent">
          POD Tools
        </h2>
      </SidebarHeader>
      <SidebarContent className="bg-sidebar">
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70 font-semibold text-sm uppercase tracking-wider">
            General
          </SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem className={cn(
              "rounded-lg mx-2 transition-colors",
              location.pathname === "/app" && "bg-sidebar-accent border border-sidebar-ring/30"
            )}>
              <SidebarMenuButton asChild>
                <Link to="/app" className={cn(
                  "flex items-center gap-3 px-3 py-2 text-sidebar-foreground/70 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rounded-lg transition-colors",
                  location.pathname === "/app" && "text-sidebar-primary font-medium"
                )}>
                  <Home className="h-5 w-5" />
                  <span>Dashboard</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70 font-semibold text-sm uppercase tracking-wider">
            Management
          </SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem className={cn(
              "rounded-lg mx-2 transition-colors",
              location.pathname === "/app/pricing-tool" && "bg-sidebar-accent border border-sidebar-ring/30"
            )}>
              <SidebarMenuButton asChild>
                <Link to="/app/pricing-tool" className={cn(
                  "flex items-center gap-3 px-3 py-2 text-sidebar-foreground/70 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rounded-lg transition-colors",
                  location.pathname === "/app/pricing-tool" && "text-sidebar-primary font-medium"
                )}>
                  <DollarSign className="h-5 w-5" />
                  <span>Printify Pricing</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
        <SidebarGroup>
          <SidebarGroupLabel className="text-sidebar-foreground/70 font-semibold text-sm uppercase tracking-wider">
            System
          </SidebarGroupLabel>
          <SidebarMenu>
            <SidebarMenuItem className={cn(
              "rounded-lg mx-2 transition-colors",
              location.pathname === "/app/settings" && "bg-sidebar-accent border border-sidebar-ring/30"
            )}>
              <SidebarMenuButton asChild>
                <Link to="/app/settings" className={cn(
                  "flex items-center gap-3 px-3 py-2 text-sidebar-foreground/70 hover:text-sidebar-foreground hover:bg-sidebar-accent/50 rounded-lg transition-colors",
                  location.pathname === "/app/settings" && "text-sidebar-primary font-medium"
                )}>
                  <Settings className="h-5 w-5" />
                  <span>Settings</span>
                </Link>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="p-4 bg-sidebar border-t border-sidebar-border">
        <UserProfileSection />
      </SidebarFooter>
    </Sidebar>
  );
}
