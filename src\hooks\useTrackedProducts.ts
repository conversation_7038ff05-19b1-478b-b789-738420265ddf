
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { TrackedProduct } from '@/types/database';

export const useTrackedProducts = () => {
  return useQuery({
    queryKey: ['tracked-products'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tracked_products')
        .select(`
          *,
          blueprints (
            id,
            title,
            brand,
            model
          ),
          print_providers (
            id,
            title,
            location
          )
        `)
        .eq('is_active', true)
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data as TrackedProduct[];
    },
  });
};

export const useDeleteTrackedProduct = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (productId: string) => {
      const { error } = await supabase
        .from('tracked_products')
        .delete()
        .eq('id', productId);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tracked-products'] });
    },
  });
};

export const useDeleteMultipleTrackedProducts = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (productIds: string[]) => {
      const { error } = await supabase
        .from('tracked_products')
        .delete()
        .in('id', productIds);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tracked-products'] });
    },
  });
};

export const useUpdateMultipleTrackedProducts = () => {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (updates: { id: string, data: Partial<Omit<TrackedProduct, 'id'>> }[]) => {
      const promises = updates.map(update =>
        supabase
          .from('tracked_products')
          .update(update.data)
          .eq('id', update.id)
      );
      
      const results = await Promise.all(promises);
      const errorResult = results.find(res => res.error);
      
      if (errorResult) {
        console.error('Error updating products:', errorResult.error);
        throw errorResult.error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tracked-products'] });
    },
  });
};
