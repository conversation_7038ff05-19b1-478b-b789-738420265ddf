
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useMarkets, useDeleteMarket } from '@/hooks/useMarkets';
import { Market } from '@/types/database';
import { formatFeeDisplay } from '@/utils/feeCalculator';
import { Pencil, Trash2, PlusCircle } from 'lucide-react';
import { MarketFormDialog } from '@/components/MarketFormDialog';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog"
import { useToast } from "@/hooks/use-toast";

const MarketsPage = () => {
  const { data: markets = [], isLoading } = useMarkets();
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedMarket, setSelectedMarket] = useState<Market | null>(null);
  const deleteMarketMutation = useDeleteMarket();
  const { toast } = useToast();

  const handleEdit = (market: Market) => {
    setSelectedMarket(market);
    setIsFormOpen(true);
  };

  const handleCreate = () => {
    setSelectedMarket(null);
    setIsFormOpen(true);
  };
  
  const handleDelete = (marketId: string) => {
    deleteMarketMutation.mutate(marketId, {
      onSuccess: () => {
        toast({
          title: "Market deleted",
          description: "The market has been successfully deleted.",
        });
      },
      onError: (error: Error) => {
         toast({
          title: "Error deleting market",
          description: error.message,
          variant: "destructive"
        });
      }
    });
  }

  return (
    <div className="p-6 sm:p-10">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold text-white">Manage Markets</h1>
          <p className="text-gray-400">Create, update, or delete your sales markets.</p>
        </div>
        <Button onClick={handleCreate} className="bg-blue-600 hover:bg-blue-700 text-white">
          <PlusCircle className="h-4 w-4 mr-2" />
          Create Market
        </Button>
      </div>

      <div className="bg-gray-800/30 backdrop-blur-sm rounded-lg border border-gray-700 overflow-hidden">
        <Table>
          <TableHeader className="bg-gray-800 border-b border-gray-700">
            <TableRow className="border-b-gray-700 hover:bg-gray-700/50">
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">Name</TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">Transaction Fee</TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">Processing Fee</TableHead>
              <TableHead className="text-gray-200 font-bold text-sm uppercase tracking-wider">Other Fee</TableHead>
              <TableHead className="text-right text-gray-200 font-bold text-sm uppercase tracking-wider">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableRow className="bg-gray-900/50">
                <TableCell colSpan={5} className="text-center py-10 text-gray-400">Loading markets...</TableCell>
              </TableRow>
            ) : markets.length === 0 ? (
                <TableRow className="bg-gray-900/50">
                  <TableCell colSpan={5} className="text-center py-10 text-gray-400">No markets found. Create one to get started.</TableCell>
                </TableRow>
            ) : (
              markets.map((market, index) => (
                <TableRow 
                  key={market.id} 
                  className={`border-b-gray-800 hover:bg-gray-700/30 transition-colors ${
                    index % 2 === 0 ? 'bg-gray-900/30' : 'bg-gray-800/30'
                  }`}
                >
                  <TableCell className="font-medium text-gray-100">{market.name}</TableCell>
                  <TableCell className="text-gray-300">{formatFeeDisplay(market.transaction_fee, market.transaction_fee_type)}</TableCell>
                  <TableCell className="text-gray-300">{formatFeeDisplay(market.processing_fee, market.processing_fee_type)}</TableCell>
                  <TableCell className="text-gray-300">{formatFeeDisplay(market.other_fee, market.other_fee_type)}</TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        onClick={() => handleEdit(market)}
                        className="text-blue-400 hover:text-blue-300 hover:bg-blue-400/10"
                      >
                        <Pencil className="h-4 w-4" />
                      </Button>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button 
                            variant="ghost" 
                            size="icon" 
                            className="text-red-400 hover:text-red-300 hover:bg-red-400/10"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="bg-gray-800 text-white border-gray-700">
                          <AlertDialogHeader>
                            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                            <AlertDialogDescription className="text-gray-400">
                              This action cannot be undone. This will permanently delete the market.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel className="bg-gray-700 text-gray-300 hover:bg-gray-600 border-gray-600">
                              Cancel
                            </AlertDialogCancel>
                            <AlertDialogAction 
                              onClick={() => handleDelete(market.id)} 
                              className="bg-red-600 hover:bg-red-700 text-white"
                            >
                              Delete
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      <MarketFormDialog 
        isOpen={isFormOpen}
        setIsOpen={setIsFormOpen}
        market={selectedMarket}
      />
    </div>
  );
};

export default MarketsPage;
