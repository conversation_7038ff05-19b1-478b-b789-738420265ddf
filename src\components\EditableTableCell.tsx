
import React, { useState, useEffect } from 'react';
import { Input } from '@/components/ui/input';

interface EditableTableCellProps {
  initialValue: number | string;
  onSave: (newValue: number) => void;
  prefix?: string;
  suffix?: string;
  className?: string;
  disabled?: boolean;
}

export const EditableTableCell = ({ initialValue, onSave, prefix, suffix, className, disabled = false }: EditableTableCellProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [value, setValue] = useState(String(initialValue));

  useEffect(() => {
    setValue(String(initialValue));
  }, [initialValue]);

  const handleSave = () => {
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue) && numericValue !== Number(initialValue)) {
      onSave(numericValue);
    }
    setIsEditing(false);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSave();
    } else if (e.key === 'Escape') {
      setValue(String(initialValue));
      setIsEditing(false);
    }
  };
  
  const handleCellClick = () => {
    if (!disabled) {
      setIsEditing(true);
    }
  }

  if (isEditing) {
    return (
      <td className={`p-0 align-middle ${className}`}>
        <div className="relative h-full">
          {prefix && <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-400 text-sm">{prefix}</span>}
          <Input
            type="number"
            value={value}
            onChange={(e) => setValue(e.target.value)}
            onBlur={handleSave}
            onKeyDown={handleKeyDown}
            autoFocus
            className={`bg-gray-700 border-gray-600 h-full py-4 text-white w-full text-base md:text-sm rounded-none border-x-0 border-y-transparent focus-visible:ring-1 focus-visible:ring-ring ${prefix ? 'pl-10' : ''} ${suffix ? 'pr-8' : ''}`}
          />
          {suffix && <span className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 text-sm">{suffix}</span>}
        </div>
      </td>
    );
  }
  
  const displayValue = typeof initialValue === 'number' ? initialValue.toFixed(2) : initialValue;

  return (
    <td onClick={handleCellClick} className={`p-4 align-middle ${disabled ? 'cursor-not-allowed' : 'cursor-pointer hover:bg-gray-700/50'} ${className}`}>
      {prefix}{displayValue}{suffix}
    </td>
  );
};
