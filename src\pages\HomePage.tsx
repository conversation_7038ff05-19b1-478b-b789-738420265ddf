
import { Link, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { ArrowR<PERSON>, DollarSign, <PERSON><PERSON><PERSON>, Zap } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { useEffect } from 'react';

const HomePage = () => {
  const { user, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && user) {
      navigate('/app', { replace: true });
    }
  }, [user, loading, navigate]);

  if (loading || (!loading && user)) {
    return (
      <div className="bg-gray-900 text-white min-h-screen flex items-center justify-center">
        {/* Loading... */}
      </div>
    );
  }

  return (
    <div className="bg-gray-900 text-white min-h-screen flex flex-col">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-gray-900/80 backdrop-blur-sm">
        <div className="container mx-auto px-6 py-4 flex justify-between items-center">
          <h1 className="text-2xl font-bold">POD Tools</h1>
          <nav className="hidden md:flex items-center gap-6">
            <a href="#features" className="text-gray-300 hover:text-white transition-colors">Features</a>
            <Button asChild variant="outline" size="sm" className="border-gray-600 text-gray-300 hover:bg-gray-700 hover:text-white">
              <Link to="/auth">Sign In</Link>
            </Button>
            <Button asChild size="sm">
              <Link to="/auth">Get Started <ArrowRight className="ml-2" /></Link>
            </Button>
          </nav>
          <div className="md:hidden">
             <Button asChild size="sm">
              <Link to="/auth">Sign In</Link>
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="container mx-auto px-6 py-24 sm:py-32 text-center">
          <div className="max-w-3xl mx-auto">
            <h2 className="text-4xl sm:text-5xl md:text-6xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-b from-white to-gray-400">
              Supercharge Your Print-on-Demand Business
            </h2>
            <p className="mt-6 text-lg text-gray-300">
              Powerful, easy-to-use tools designed to help you optimize pricing, track profits, and scale your POD store with confidence.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button asChild size="lg">
                <Link to="/auth">
                  Start for Free
                </Link>
              </Button>
              <Button asChild variant="ghost" size="lg">
                <a href="#features">
                  Learn more <ArrowRight className="ml-2" />
                </a>
              </Button>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="container mx-auto px-6 py-24 sm:py-32">
          <div className="max-w-4xl mx-auto text-center">
            <h3 className="text-3xl font-bold tracking-tight">Everything you need to succeed</h3>
            <p className="mt-4 text-gray-400">
              From calculating complex fees to managing your product listings, we've got you covered.
            </p>
          </div>
          <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-800/50 p-8 rounded-lg border border-gray-700 hover:border-primary transition-colors">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary text-primary-foreground mb-6">
                <DollarSign />
              </div>
              <h4 className="text-xl font-bold">Printify Pricing Tool</h4>
              <p className="mt-2 text-gray-400">
                Effortlessly calculate product prices, profit margins, and fees for Etsy, Shopify, and more.
              </p>
            </div>
            <div className="bg-gray-800/50 p-8 rounded-lg border border-gray-700 hover:border-primary transition-colors">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary text-primary-foreground mb-6">
                <BarChart />
              </div>
              <h4 className="text-xl font-bold">Profit Analytics</h4>
              <p className="mt-2 text-gray-400">
                (Coming Soon) Track your sales and profit trends over time with insightful dashboards.
              </p>
            </div>
            <div className="bg-gray-800/50 p-8 rounded-lg border border-gray-700 hover:border-primary transition-colors">
              <div className="flex items-center justify-center h-12 w-12 rounded-lg bg-primary text-primary-foreground mb-6">
                <Zap />
              </div>
              <h4 className="text-xl font-bold">More Tools on the Way</h4>
              <p className="mt-2 text-gray-400">
                We're constantly building new tools to help you grow your POD empire.
              </p>
            </div>
          </div>
        </section>
        
        {/* Call to Action */}
        <section className="container mx-auto px-6 py-24 sm:py-32">
          <div className="relative isolate overflow-hidden bg-gray-800/50 border border-primary/50 rounded-2xl px-6 py-24 text-center shadow-2xl sm:px-16">
            <h2 className="mx-auto max-w-2xl text-3xl font-bold tracking-tight text-white sm:text-4xl">
              Ready to take control of your pricing?
            </h2>
            <p className="mx-auto mt-6 max-w-xl text-lg leading-8 text-gray-300">
              Sign up today and get instant access to the Printify Pricing Tool. It's free to get started.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              <Button asChild size="lg">
                <Link to="/auth">
                  Create your free account
                </Link>
              </Button>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-900 border-t border-gray-800">
        <div className="container mx-auto px-6 py-8 text-center text-gray-400">
          <p>&copy; {new Date().getFullYear()} POD Tools. All rights reserved.</p>
        </div>
      </footer>
    </div>
  );
};

export default HomePage;
