
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/hooks/use-toast';

interface CostData {
  variantId: number;
  productionCost: number;
  shippingCost: number;
}

interface Variant {
  id: number;
  title: string;
  options: {
    color?: string;
    size?: string;
  };
}

export const useAutoCostCalculation = () => {
  const [isGettingCosts, setIsGettingCosts] = useState(false);
  const { toast } = useToast();

  const groupVariantsBySize = (variants: Variant[]) => {
    const sizeGroups: Record<string, Variant[]> = {};
    
    variants.forEach(variant => {
      const size = variant.options?.size || 'default';
      if (!sizeGroups[size]) {
        sizeGroups[size] = [];
      }
      sizeGroups[size].push(variant);
    });
    
    return sizeGroups;
  };

  const selectRepresentativeVariants = (sizeGroups: Record<string, Variant[]>) => {
    const representatives: Variant[] = [];
    
    Object.values(sizeGroups).forEach(sizeVariants => {
      // Select the first variant from each size group as representative
      representatives.push(sizeVariants[0]);
    });
    
    return representatives;
  };

  const applyCostsToAllVariants = (
    allVariants: Variant[], 
    representativeCosts: CostData[], 
    sizeGroups: Record<string, Variant[]>
  ): CostData[] => {
    const allCosts: CostData[] = [];
    
    // Create a map of size -> cost data
    const sizeCostMap: Record<string, { productionCost: number; shippingCost: number }> = {};
    
    representativeCosts.forEach(costData => {
      // Find which size this representative variant belongs to
      for (const [size, variants] of Object.entries(sizeGroups)) {
        if (variants.some(v => v.id === costData.variantId)) {
          sizeCostMap[size] = {
            productionCost: costData.productionCost,
            shippingCost: costData.shippingCost
          };
          break;
        }
      }
    });
    
    // Apply costs to all variants based on their size
    allVariants.forEach(variant => {
      const size = variant.options?.size || 'default';
      const costData = sizeCostMap[size];
      
      if (costData) {
        allCosts.push({
          variantId: variant.id,
          productionCost: costData.productionCost,
          shippingCost: costData.shippingCost
        });
      } else {
        // Fallback if no cost data found
        allCosts.push({
          variantId: variant.id,
          productionCost: 0,
          shippingCost: 0
        });
      }
    });
    
    return allCosts;
  };

  const getCostsAutomatically = useCallback(async (
    blueprintId: number,
    printProviderId: number,
    variantIds: number[],
    onCostsCalculated?: (costs: CostData[]) => void
  ) => {
    if (variantIds.length === 0) return;

    setIsGettingCosts(true);
    try {
      // Get the first available store
      const storesResponse = await supabase.functions.invoke('printify-catalog', {
        body: { action: 'get-stores' }
      });
      
      if (storesResponse.error || !storesResponse.data?.length) {
        console.log('No stores available for cost retrieval');
        return;
      }

      const store = storesResponse.data[0];
      console.log('Getting costs using store:', store.title);

      // Get variant details to group by size
      const variantsResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'get-blueprint-variants',
          blueprintId,
          printProviderId
        }
      });
      
      if (variantsResponse.error) {
        console.error('Failed to get variants for grouping:', variantsResponse.error);
        return;
      }

      const allVariantsData = variantsResponse.data?.variants || [];
      const requestedVariants = allVariantsData.filter((v: Variant) => variantIds.includes(v.id));
      
      console.log(`Processing ${requestedVariants.length} variants for cost retrieval`);
      
      // Group variants by size
      const sizeGroups = groupVariantsBySize(requestedVariants);
      console.log('Size groups:', Object.keys(sizeGroups).map(size => `${size}: ${sizeGroups[size].length} variants`));
      
      // Select representative variants (one per size)
      const representativeVariants = selectRepresentativeVariants(sizeGroups);
      const representativeVariantIds = representativeVariants.map(v => v.id);
      
      console.log(`Using ${representativeVariants.length} representative variants for cost retrieval:`, 
        representativeVariants.map(v => `${v.options?.size || 'default'} (${v.options?.color || 'no color'})`));

      // Create temporary product with representative variants only
      const createResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'create-placeholder-product',
          storeId: store.id,
          blueprintId,
          printProviderId,
          variantIds: representativeVariantIds
        }
      });
      
      if (createResponse.error) {
        console.error('Failed to create product for cost retrieval:', createResponse.error);
        return;
      }
      
      const productId = createResponse.data?.id;
      if (!productId) return;

      // Get costs for representative variants
      const costsResponse = await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'get-product-costs',
          storeId: store.id,
          productId
        }
      });
      
      if (costsResponse.error) {
        console.error('Failed to get costs:', costsResponse.error);
        return;
      }
      
      const productData = costsResponse.data;
      const representativeCosts = productData.variants
        ?.filter((variant: any) => representativeVariantIds.includes(variant.id))
        .map((variant: any) => ({
          variantId: variant.id,
          productionCost: variant.cost || 0,
          shippingCost: 0
        })) || [];

      console.log('Representative costs retrieved:', representativeCosts);

      // Apply costs to all variants based on size grouping
      const allCosts = applyCostsToAllVariants(requestedVariants, representativeCosts, sizeGroups);
      
      console.log(`Applied costs to all ${allCosts.length} variants based on size grouping`);
      onCostsCalculated?.(allCosts);

      // Clean up
      await supabase.functions.invoke('printify-catalog', {
        body: { 
          action: 'delete-placeholder-product',
          storeId: store.id,
          productId
        }
      });

    } catch (error) {
      console.error('Error getting costs:', error);
    } finally {
      setIsGettingCosts(false);
    }
  }, [toast]);

  return {
    getCostsAutomatically,
    isGettingCosts
  };
};
