import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Initialize Supabase client
    const supabaseUrl = Deno.env.get('SUPABASE_URL')!
    const supabaseKey = Deno.env.get('SUPABASE_ANON_KEY')!
    const supabase = createClient(supabaseUrl, supabaseKey, {
      global: { headers: { Authorization: req.headers.get('Authorization')! } }
    })

    // Get the authenticated user
    const { data: { user }, error: authError } = await supabase.auth.getUser()
    if (authError || !user) {
      throw new Error('Authentication required')
    }

    // Get user's Printify API key
    const { data: apiKeyData, error: keyError } = await supabase
      .from('user_api_keys')
      .select('api_key_encrypted')
      .eq('service_name', 'printify')
      .single()

    if (keyError || !apiKeyData?.api_key_encrypted) {
      throw new Error('Printify API key not configured. Please add your API key in settings.')
    }

    const printifyApiKey = apiKeyData.api_key_encrypted
    console.log('Using API key (first 10 chars):', printifyApiKey.substring(0, 10) + '...')

    const { action, search, blueprintId, printProviderId, storeId, variantIds, productId } = await req.json()

    const headers = {
      'Authorization': `Bearer ${printifyApiKey}`,
      'Content-Type': 'application/json'
    }

    let response

    switch (action) {
      case 'get-stores': {
        console.log('Fetching Printify stores')
        response = await fetch('https://api.printify.com/v1/shops.json', {
          headers,
          method: 'GET'
        })
        break
      }

      case 'upload-placeholder-image': {
        console.log('Uploading placeholder image to Printify')
        
        // Create a simple 1x1 pixel transparent PNG as base64
        const placeholderImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        
        const imageData = {
          file_name: 'placeholder.png',
          contents: placeholderImageBase64
        }

        response = await fetch('https://api.printify.com/v1/uploads/images.json', {
          method: 'POST',
          headers,
          body: JSON.stringify(imageData)
        })
        break
      }

      case 'create-placeholder-product': {
        if (!storeId || !blueprintId || !printProviderId || !variantIds || variantIds.length === 0) {
          throw new Error('Store ID, Blueprint ID, Print Provider ID, and Variant IDs are required')
        }

        console.log('Creating placeholder product for cost calculation')
        
        // First, upload a placeholder image
        console.log('Uploading placeholder image...')
        const placeholderImageBase64 = 'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
        
        const imageUploadResponse = await fetch('https://api.printify.com/v1/uploads/images.json', {
          method: 'POST',
          headers,
          body: JSON.stringify({
            file_name: 'placeholder.png',
            contents: placeholderImageBase64
          })
        })

        if (!imageUploadResponse.ok) {
          const errorText = await imageUploadResponse.text()
          console.error('Image upload failed:', imageUploadResponse.status, errorText)
          throw new Error(`Failed to upload placeholder image: ${imageUploadResponse.statusText}`)
        }

        const uploadedImage = await imageUploadResponse.json()
        console.log('Uploaded image ID:', uploadedImage.id)
        
        // Now create the product with the uploaded image
        const productData = {
          title: `Cost Calculator - ${Date.now()}`,
          description: "Temporary product for cost calculation",
          blueprint_id: blueprintId,
          print_provider_id: printProviderId,
          variants: variantIds.map((variantId: number) => ({
            id: variantId,
            price: 1000, // $10.00 placeholder price
            is_enabled: true
          })),
          print_areas: [
            {
              variant_ids: variantIds,
              placeholders: [
                {
                  position: "front",
                  images: [
                    {
                      id: uploadedImage.id,
                      x: 0.5,
                      y: 0.5,
                      scale: 0.1,
                      angle: 0
                    }
                  ]
                }
              ]
            }
          ]
        }

        console.log('Product data:', JSON.stringify(productData, null, 2))

        response = await fetch(`https://api.printify.com/v1/shops/${storeId}/products.json`, {
          method: 'POST',
          headers,
          body: JSON.stringify(productData)
        })
        break
      }

      case 'get-product-costs': {
        if (!storeId || !productId) {
          throw new Error('Store ID and Product ID are required')
        }

        console.log('Fetching product costs for product:', productId)
        response = await fetch(`https://api.printify.com/v1/shops/${storeId}/products/${productId}.json`, {
          headers,
          method: 'GET'
        })
        break
      }

      case 'delete-placeholder-product': {
        if (!storeId || !productId) {
          throw new Error('Store ID and Product ID are required')
        }

        console.log('Deleting placeholder product:', productId)
        response = await fetch(`https://api.printify.com/v1/shops/${storeId}/products/${productId}.json`, {
          method: 'DELETE',
          headers
        })
        break
      }

      case 'search-blueprints': {
        if (!search) throw new Error('Search term required')
        
        console.log('Searching for blueprints with term:', search)
        console.log('Making request to:', 'https://api.printify.com/v1/catalog/blueprints.json')
        
        // First get all blueprints
        const allBlueprintsResponse = await fetch('https://api.printify.com/v1/catalog/blueprints.json', {
          headers,
          method: 'GET'
        })
        
        console.log('Response status:', allBlueprintsResponse.status)
        console.log('Response status text:', allBlueprintsResponse.statusText)
        
        if (!allBlueprintsResponse.ok) {
          const errorText = await allBlueprintsResponse.text()
          console.error('Printify API error response:', errorText)
          throw new Error(`Printify API error: ${allBlueprintsResponse.status} ${allBlueprintsResponse.statusText} - ${errorText}`)
        }
        
        const responseText = await allBlueprintsResponse.text()
        console.log('Raw response (first 500 chars):', responseText.substring(0, 500))
        
        let allBlueprints
        try {
          allBlueprints = JSON.parse(responseText)
        } catch (parseError) {
          console.error('Failed to parse JSON response:', parseError)
          throw new Error('Invalid JSON response from Printify API')
        }
        
        console.log('Response is array:', Array.isArray(allBlueprints))
        console.log('Total blueprints received:', Array.isArray(allBlueprints) ? allBlueprints.length : (allBlueprints.data?.length || 0))
        
        // Handle both possible response structures
        let blueprintsArray
        if (Array.isArray(allBlueprints)) {
          // Direct array response
          blueprintsArray = allBlueprints
        } else if (allBlueprints.data && Array.isArray(allBlueprints.data)) {
          // Object with data property
          blueprintsArray = allBlueprints.data
        } else {
          console.log('Unexpected response structure:', JSON.stringify(allBlueprints, null, 2))
          throw new Error('Unexpected response structure from Printify API')
        }
        
        // Log first few blueprints to see structure
        if (blueprintsArray.length > 0) {
          console.log('Sample blueprint structure:', JSON.stringify(blueprintsArray[0], null, 2))
        }
        
        // Filter blueprints based on search term with more flexible matching
        const searchTerm = search.toLowerCase().trim()
        const filteredBlueprints = blueprintsArray.filter((blueprint: any) => {
          const title = (blueprint.title || '').toLowerCase()
          const description = (blueprint.description || '').toLowerCase()
          const brand = (blueprint.brand || '').toLowerCase()
          const model = (blueprint.model || '').toLowerCase()
          const tags = Array.isArray(blueprint.tags) ? blueprint.tags.join(' ').toLowerCase() : ''
          
          // Create searchable text from all fields
          const searchableText = `${title} ${description} ${brand} ${model} ${tags}`.toLowerCase()
          
          console.log(`Checking blueprint: "${title}" against search term: "${searchTerm}"`)
          
          const matches = searchableText.includes(searchTerm)
          if (matches) {
            console.log(`✓ Match found: ${title}`)
          }
          
          return matches
        })
        
        console.log('Filtered blueprints count:', filteredBlueprints.length)
        
        // If no matches found, try partial matching with individual words
        if (filteredBlueprints.length === 0) {
          console.log('No exact matches, trying partial matching...')
          const searchWords = searchTerm.split(' ').filter(word => word.length > 2)
          
          const partialMatches = blueprintsArray.filter((blueprint: any) => {
            const title = (blueprint.title || '').toLowerCase()
            const description = (blueprint.description || '').toLowerCase()
            
            return searchWords.some(word => 
              title.includes(word) || description.includes(word)
            )
          })
          
          console.log('Partial matches found:', partialMatches.length)
          
          // Return filtered results in the same format as the original API
          return new Response(JSON.stringify({ 
            data: partialMatches.slice(0, 50) // Limit to 50 results
          }), {
            headers: { ...corsHeaders, 'Content-Type': 'application/json' },
          })
        }
        
        // Return filtered results in the same format as the original API
        return new Response(JSON.stringify({ 
          data: filteredBlueprints.slice(0, 50) // Limit to 50 results
        }), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }
      
      case 'get-blueprint-providers': {
        if (!blueprintId) throw new Error('Blueprint ID required')
        response = await fetch(`https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers.json`, {
          headers
        })
        break
      }
      
      case 'get-blueprint-variants': {
        if (!blueprintId || !printProviderId) throw new Error('Blueprint ID and Print Provider ID required')
        
        // Get variants
        const variantsResponse = await fetch(`https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers/${printProviderId}/variants.json`, {
          headers
        })
        
        if (!variantsResponse.ok) {
          const errorText = await variantsResponse.text()
          console.error('Variants API error:', variantsResponse.status, errorText)
          throw new Error(`Failed to fetch variants: ${variantsResponse.statusText}`)
        }
        
        const variantsData = await variantsResponse.json()
        console.log('Variants data received:', variantsData)
        
        // Get shipping information
        let shippingData = null
        try {
          const shippingResponse = await fetch(`https://api.printify.com/v1/catalog/blueprints/${blueprintId}/print_providers/${printProviderId}/shipping.json`, {
            headers
          })
          
          if (shippingResponse.ok) {
            shippingData = await shippingResponse.json()
            console.log('Shipping data received:', shippingData)
          } else {
            console.log('Could not fetch shipping data:', shippingResponse.status, shippingResponse.statusText)
          }
        } catch (shippingError) {
          console.log('Shipping API error (non-fatal):', shippingError)
        }
        
        // Enhance variants with shipping costs if available
        if (variantsData.variants && shippingData && shippingData.profiles) {
          variantsData.variants = variantsData.variants.map((variant: any) => {
            // Try to find shipping cost for this variant
            let shippingCost = 0
            
            // Look through shipping profiles to find costs
            for (const profile of shippingData.profiles) {
              if (profile.variant_ids && profile.variant_ids.includes(variant.id)) {
                // Found shipping profile for this variant
                if (profile.first_item && profile.first_item.cost) {
                  shippingCost = profile.first_item.cost / 100 // Convert cents to dollars
                }
                break
              }
            }
            
            return {
              ...variant,
              shipping_cost: shippingCost,
              production_cost_note: "Production cost will be available after creating a product with artwork"
            }
          })
        }
        
        return new Response(JSON.stringify(variantsData), {
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        })
      }
      
      default:
        throw new Error('Invalid action')
    }

    if (response && !response.ok) {
      const errorText = await response.text()
      console.error('Printify API error:', response.status, errorText)
      
      if (response.status === 401) {
        throw new Error('Invalid Printify API key. Please check your API key in settings.')
      } else if (response.status === 403) {
        throw new Error('Access denied. Please verify your Printify API key permissions.')
      } else {
        throw new Error(`Printify API error: ${response.statusText}`)
      }
    }

    if (response) {
      const data = await response.json()
      return new Response(JSON.stringify(data), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      })
    }

  } catch (error) {
    console.error('Function error:', error.message)
    return new Response(JSON.stringify({ error: error.message }), {
      status: 400,
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
    })
  }
})
